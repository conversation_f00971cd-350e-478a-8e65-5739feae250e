#!/usr/bin/env python
"""
Example script for checking whether a model config builds

This script demonstrates:
1. Builds a model from a config
2. Prepares data
3. Tests the forward and backward on one batch
4. Tests the jacobian
"""
#%%
import torch
from torch.utils.data import DataLoader

import lightning as pl

from pathlib import Path
import numpy as np
import matplotlib.pyplot as plt

from DataYatesV1.models import build_model, initialize_model_components
from DataYatesV1.models.config_loader import load_config
from DataYatesV1.models.lightning import PLCoreVisionModel
from DataYatesV1.models.checkpoint import load_model_from_checkpoint, test_model_consistency, find_best_checkpoint
from DataYatesV1.models.model_manager import ModelRegistry
from DataYatesV1.utils.data import prepare_data
from DataYatesV1.utils.general import ensure_tensor

# Set random seed for reproducibility
torch.manual_seed(42)
np.random.seed(42)

# Check if CUDA is available
device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
print(f"Using device: {device}")

#%%


def create_dataloaders(train_dset, val_dset, batch_size=256):
    """Create DataLoader objects for training."""
    train_loader = DataLoader(
        train_dset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=4,
        pin_memory=True
    )

    val_loader = DataLoader(
        val_dset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )

    return train_loader, val_loader

#%%
import yaml


# Prepare data
from DataYatesV1 import get_complete_sessions
sessions = get_complete_sessions()
sess = sessions[9]

# config_path = Path("configs/v1_cones_dense_gru_eyevel.yaml")
# config_path = Path("configs/test_model_stimembed_3dconv.yaml")
# config_path = Path("configs/test_model_stimembedcones_3dconv_big_ei.yaml")
config_path = Path("configs/test_model_stimembed_x3d.yaml")
# config_path = Path("configs/test_model_stimembedcones_adapter_3dconv_big_ei.yaml")

# config_path = Path("configs/v1_cones_dense_gaussian_standard_mish.yaml")
dataset_config_path = Path("configs/test_data_stimembed_symlog_5lags.yaml")
# dataset_config_path = Path("configs/test_data_stimcones_symlog_5lags.yaml")

config = load_config(config_path)
model = build_model(config).to(device)

#%%
with open(dataset_config_path, 'r') as f:
    dataset_config = yaml.safe_load(f)

# Prepare data using the dataset configuration
train_dset, val_dset, dataset_config = prepare_data(dataset_config)
train_loader, val_loader = create_dataloaders(train_dset, val_dset)


# Update config with number of units
config['readout']['params']['n_units'] = len(dataset_config['cids'])
model = build_model(config).to(device)

#%%
batch = next(iter(train_loader))
batch = {k: v.to(device) for k, v in batch.items() if isinstance(v, torch.Tensor)}

# Test forward pass
model.eval()
with torch.no_grad():
    output = model(batch['stim'])
    print(f"Output shape: {output.shape}")
#%%

_ = plt.plot(output.detach().cpu().numpy())

#%%
nbasis = batch['stim'].shape[1]
plt.figure(figsize=(10, 3))
for i in range(nbasis):
    plt.subplot(1, nbasis, i+1)
    plt.imshow(batch['stim'][40, i, 0].cpu().numpy(), cmap='gray')
    plt.axis('off')
    plt.title(f'Basis {i}')
plt.show()

#%%
from torch.profiler import profile, ProfilerActivity


with torch.no_grad():

    with profile(
        activities=[ProfilerActivity.CPU, ProfilerActivity.CUDA],
        record_shapes=True,
        profile_memory=True,
    ) as prof:

        out = model(batch['stim'])                 # 1 forward pass is fine

print( prof.key_averages()
            .table(sort_by="self_cuda_time_total", row_limit=20) )

#%%
if not isinstance(model.recurrent, torch.nn.Identity):
    print("Compiling recurrent...")
    model.recurrent = torch.compile(model.recurrent, mode="reduce-overhead")

print("Compiling convnet...")
model.convnet = torch.compile(model.convnet, mode="default")

#%%
# -------- warm-up --------
with torch.no_grad():
    model(batch['stim'])     # traces + compiles
    model(batch['stim'])     # runs once to materialise caches
    torch.cuda.synchronize()
# --------------------------
#%%
from torch.profiler import profile, ProfilerActivity


with torch.no_grad():

    with profile(
        activities=[ProfilerActivity.CPU, ProfilerActivity.CUDA],
        record_shapes=True,
        profile_memory=True,
    ) as prof:

        out = model(batch['stim'])                 # 1 forward pass is fine

print( prof.key_averages()
            .table(sort_by="self_cuda_time_total", row_limit=20) )

#%%
import sys
sys.exit()
#%%
#%%
inv_softplus = lambda x, beta=1: torch.log(torch.exp(beta*x) - 1) / beta

# Create Lightning module
pl_model = PLCoreVisionModel(
    model_class=build_model,  # Pass the factory function
    model_config=config,      # Pass the configuration
    optimizer='AdamW',
    optim_kwargs={'lr': 5e-4, 'weight_decay': 1e-4},
    accumulate_grad_batches=1,
    dataset_info=dataset_config  # Pass dataset information
    )

# initialize model
baseline_rates = train_dset[:]['robs'].mean(0)
initialize_model_components(pl_model.model, init_bias=inv_softplus(baseline_rates))
init_bi = ensure_tensor(inv_softplus(baseline_rates), device=pl_model.model.readout.bias.device)
pl_model.model.readout.bias.data = init_bi
# sess = get_session('Allen', '2022-04-13')
# %%
