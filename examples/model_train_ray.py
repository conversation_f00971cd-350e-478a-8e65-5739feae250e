#!/usr/bin/env python
"""
Hyperparameter tuning for neural network models using Ray and Optuna

This script demonstrates:
1. Hyperparameter optimization using Ray Tune + Optuna
2. Support for multiple architecture types (ResNet, DenseNet, X3D)
3. Robust logging and checkpointing for week-long runs
4. Integration with existing model registry
5. Resumable training with proper state management

Usage:
    python model_train_ray.py --dataset-config /path/to/dataset.yaml --model-config /path/to/model.yaml --num-trials 100

Key hyperparameters tuned:
- Learning rate (1e-5 to 1e-2)
- Weight decay (1e-6 to 1e-3) 
- Architecture type (resnet, densenet, x3d)
- Architecture-specific parameters (depth, width, etc.)
"""

import os
import sys
import time
import argparse
import yaml
from pathlib import Path
from typing import Dict, Any, Optional, List
import numpy as np
import torch
from torch.utils.data import DataLoader

# Ray and Optuna imports
import ray
from ray import tune
from ray.tune.schedulers import ASHAScheduler
from ray.tune.search.optuna import OptunaSearch
import optuna

# Lightning imports
import lightning as pl
from lightning.pytorch.loggers import WandbLogger
from lightning.pytorch.callbacks import ModelCheckpoint, EarlyStopping

# Local imports
from DataYatesV1.models import build_model, initialize_model_components, get_name_from_config
from DataYatesV1.models.config_loader import load_config
from DataYatesV1.models.lightning import PLCoreVisionModel
from DataYatesV1.models.model_manager import ModelRegistry
from DataYatesV1.utils.data import prepare_data
from DataYatesV1.utils.torch import get_free_device
from DataYatesV1.models.utils.general import ValidateOnTrainStart
import codename

# Set random seed for reproducibility
torch.manual_seed(42)
np.random.seed(42)

# Global registry for tracking models
registry_dir = Path("/mnt/ssd/YatesMarmoV1/conv_model_fits/model_registry_ray")
registry = ModelRegistry(registry_dir)

class RayTrainingCallback(pl.Callback):
    """Callback to report metrics to Ray Tune during training."""
    
    def __init__(self, report_frequency: int = 1):
        self.report_frequency = report_frequency
        
    def on_validation_end(self, trainer, pl_module):
        # Report metrics to Ray Tune
        if trainer.current_epoch % self.report_frequency == 0:
            metrics = {
                'val_loss': trainer.callback_metrics.get('val_loss', float('inf')).item(),
                'train_loss': trainer.callback_metrics.get('train_loss', float('inf')).item(),
                'epoch': trainer.current_epoch,
                'step': trainer.global_step
            }
            
            # Add any additional metrics that might be available
            for key, value in trainer.callback_metrics.items():
                if isinstance(value, torch.Tensor) and value.numel() == 1:
                    metrics[key] = value.item()
                    
            tune.report(**metrics)

def create_dataloaders(train_dset, val_dset, batch_size=256, num_workers=None):
    """Create DataLoader objects for training."""
    if num_workers is None:
        num_workers = min(4, os.cpu_count()//2)  # Conservative default for Ray
    
    def seed_worker(worker_id):
        worker_seed = torch.initial_seed() % 2**32
        np.random.seed(worker_seed)
        
    g = torch.Generator()
    g.manual_seed(42)
    
    train_loader = DataLoader(
        train_dset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=True,
        timeout=60 if num_workers > 0 else 0,
        worker_init_fn=seed_worker,
        generator=g
    )

    val_loader = DataLoader(
        val_dset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=True,
        timeout=60 if num_workers > 0 else 0,
        worker_init_fn=seed_worker,
        generator=g
    )

    return train_loader, val_loader

def get_search_space(base_model_config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Define the hyperparameter search space based on the model configuration.

    Args:
        base_model_config: Base model configuration to determine architecture options

    Returns:
        Dictionary defining the search space for Ray Tune
    """
    search_space = {
        # Optimizer hyperparameters
        'lr': tune.loguniform(1e-5, 1e-2),
        'weight_decay': tune.loguniform(1e-6, 1e-3),

        # Training hyperparameters
        'batch_size': tune.choice([128, 256, 512]),

        # Architecture type (skip x3d for now as it's unstable)
        'convnet_type': tune.choice(['resnet', 'densenet']),
    }

    # Architecture-specific hyperparameters based on your actual configs
    # ResNet parameters
    search_space.update({
        'resnet_channels': tune.choice([[32, 64, 128], [64, 128, 256], [32, 64, 128, 256]]),
        'resnet_dropout': tune.uniform(0.0, 0.2),
        'resnet_act_type': tune.choice(['gelu', 'mish', 'relu']),
        'resnet_norm_type': tune.choice(['rms', 'batch', 'layer']),
    })

    # DenseNet parameters
    search_space.update({
        'densenet_channels': tune.choice([[16, 16, 16], [32, 32, 32], [24, 24, 24]]),
        'densenet_dropout': tune.uniform(0.0, 0.2),
        'densenet_act_type': tune.choice(['gelu', 'mish', 'relu']),
        'densenet_norm_type': tune.choice(['rms', 'batch', 'layer']),
    })

    # X3D parameters (commented out for now)
    # search_space.update({
    #     'x3d_channels': tune.choice([[32, 64, 128], [24, 48, 96], [16, 32, 64]]),
    #     'x3d_depth': tune.choice([[2, 3, 2], [1, 2, 1], [3, 4, 3]]),
    #     'x3d_dropout': tune.uniform(0.0, 0.2),
    #     'x3d_stochastic_depth': tune.uniform(0.0, 0.2),
    # })

    return search_space

def modify_config_with_trial_params(base_config: Dict[str, Any], trial_params: Dict[str, Any]) -> Dict[str, Any]:
    """
    Modify the base configuration with trial-specific hyperparameters.

    Args:
        base_config: Base model configuration
        trial_params: Hyperparameters suggested by the trial

    Returns:
        Modified configuration for this trial
    """
    import copy
    config = copy.deepcopy(base_config)

    # Update convnet configuration based on architecture type
    convnet_type = trial_params['convnet_type']
    config['convnet']['type'] = convnet_type

    if convnet_type == 'resnet':
        # Use your actual ResNet config structure
        config['convnet']['params'] = {
            'channels': trial_params['resnet_channels'],
            'dim': 3,
            'checkpointing': True,
            'block_config': {
                'conv_params': {
                    'type': 'depthwise',
                    'kernel_size': [2, 5, 5],
                    'padding': [0, 0, 0]
                },
                'norm_type': trial_params['resnet_norm_type'],
                'act_type': trial_params['resnet_act_type'],
                'dropout': trial_params['resnet_dropout'],
                'pool_params': {
                    'type': 'avg',
                    'kernel_size': [1, 2, 2],
                    'stride': [1, 2, 2]
                }
            }
        }
    elif convnet_type == 'densenet':
        # Use your actual DenseNet config structure
        config['convnet']['params'] = {
            'channels': trial_params['densenet_channels'],
            'dim': 3,
            'checkpointing': True,
            'block_config': {
                'conv_params': {
                    'type': 'depthwise',
                    'kernel_size': [2, 5, 5],
                    'padding': [0, 0, 0]
                },
                'norm_type': trial_params['densenet_norm_type'],
                'act_type': trial_params['densenet_act_type'],
                'dropout': trial_params['densenet_dropout'],
                'pool_params': {
                    'type': 'avg',
                    'kernel_size': [1, 2, 2],
                    'stride': [1, 2, 2]
                }
            }
        }
    # X3D support commented out for now
    # elif convnet_type == 'x3d':
    #     config['convnet']['params'] = {
    #         'channels': trial_params['x3d_channels'],
    #         'depth': trial_params['x3d_depth'],
    #         'dim': 3,
    #         't_kernel': 5,
    #         's_kernel': 3,
    #         'exp_ratio': 4,
    #         'norm_type': 'grn',
    #         'act_type': 'silu',
    #         'stride_stages': [1, 2, 2],
    #         'lite_lk': False,
    #         'lk_every': 2,
    #         'dropout': trial_params['x3d_dropout'],
    #         'stochastic_depth': trial_params['x3d_stochastic_depth'],
    #         'checkpointing': True
    #     }

    return config

def calculate_baseline_rates(train_loader):
    """Calculate baseline firing rates for bias initialization."""
    fr = 0
    n = 0
    for dset in train_loader.dataset.dsets:
        fr += dset.covariates['robs'].sum(0)
        n += dset.covariates['robs'].shape[0]
    baseline_rates = fr / n
    return baseline_rates

def train_trial(config_dict: Dict[str, Any],
                base_model_config: Dict[str, Any],
                dataset_config: Dict[str, Any],
                train_dset, val_dset,
                trial_name: str,
                max_epochs: int = 25) -> None:
    """
    Train a single trial with the given hyperparameters.

    Args:
        config_dict: Trial hyperparameters from Ray Tune
        base_model_config: Base model configuration
        dataset_config: Dataset configuration
        train_dset, val_dset: Training and validation datasets
        trial_name: Unique name for this trial
        max_epochs: Maximum number of epochs to train
    """
    # Modify config with trial parameters
    model_config = modify_config_with_trial_params(base_model_config, config_dict)

    # Update number of units based on dataset
    model_config['readout']['params']['n_units'] = len(dataset_config['cids'])

    # Create dataloaders with trial batch size
    train_loader, val_loader = create_dataloaders(
        train_dset, val_dset,
        batch_size=config_dict['batch_size'],
        num_workers=2  # Conservative for Ray workers
    )

    # Calculate baseline rates for initialization
    baseline_rates = calculate_baseline_rates(train_loader)
    inv_softplus = lambda x, beta=1: torch.log(torch.exp(beta*x) - 1) / beta

    # Create Lightning module with trial hyperparameters
    pl_model = PLCoreVisionModel(
        model_class=build_model,
        model_config=model_config,
        optimizer='AdamW',
        optim_kwargs={
            'lr': config_dict['lr'],
            'weight_decay': config_dict['weight_decay']
        },
        accumulate_grad_batches=1,
        dataset_info=dataset_config
    )

    # Initialize model components
    initialize_model_components(pl_model.model, init_bias=inv_softplus(baseline_rates))

    # Create checkpoint directory for this trial
    checkpoint_dir = Path(f"/tmp/ray_trial_{trial_name}")
    checkpoint_dir.mkdir(parents=True, exist_ok=True)

    # Setup callbacks
    checkpoint_callback = ModelCheckpoint(
        dirpath=str(checkpoint_dir),
        filename=f'{trial_name}-{{epoch:02d}}-{{val_loss:.4f}}',
        monitor='val_loss',
        mode='min',
        save_top_k=1,
        save_last=False  # Save space in Ray trials
    )

    early_stopping_callback = EarlyStopping(
        monitor='val_loss',
        patience=5,  # Aggressive early stopping for trials
        verbose=False,
        mode='min'
    )

    ray_callback = RayTrainingCallback(report_frequency=1)

    callbacks = [
        checkpoint_callback,
        early_stopping_callback,
        ray_callback,
        ValidateOnTrainStart()
    ]

    # Create trainer with minimal logging for Ray
    trainer = pl.Trainer(
        callbacks=callbacks,
        accelerator='gpu' if torch.cuda.is_available() else 'cpu',
        devices=1,  # Single device per trial
        max_epochs=max_epochs,
        logger=False,  # Disable logging to save resources
        enable_progress_bar=False,  # Disable progress bar
        enable_model_summary=False,  # Disable model summary
        num_sanity_val_steps=0,  # Skip sanity check
        check_val_every_n_epoch=1,
    )

    try:
        # Train the model
        trainer.fit(pl_model, train_loader, val_loader)

        # Get final metrics
        final_val_loss = trainer.callback_metrics.get('val_loss', float('inf'))
        if isinstance(final_val_loss, torch.Tensor):
            final_val_loss = final_val_loss.item()

        # Report final result
        tune.report(
            val_loss=final_val_loss,
            epoch=trainer.current_epoch,
            done=True
        )

    except Exception as e:
        print(f"Trial {trial_name} failed with error: {e}")
        # Report failure to Ray
        tune.report(val_loss=float('inf'), done=True)

    finally:
        # Cleanup to save memory
        del pl_model, trainer, train_loader, val_loader
        torch.cuda.empty_cache() if torch.cuda.is_available() else None

def run_hyperparameter_search(dataset_config_path: str,
                             model_config_path: str,
                             num_trials: int = 100,
                             max_epochs_per_trial: int = 25,
                             num_gpus: int = None,
                             num_cpus: int = None,
                             resume: bool = False,
                             experiment_name: str = None) -> str:
    """
    Run hyperparameter search using Ray Tune + Optuna.

    Args:
        dataset_config_path: Path to dataset configuration
        model_config_path: Path to model configuration
        num_trials: Number of trials to run
        max_epochs_per_trial: Maximum epochs per trial
        num_gpus: Number of GPUs to use (auto-detect if None)
        num_cpus: Number of CPUs to use (auto-detect if None)
        resume: Whether to resume from previous experiment
        experiment_name: Name for the experiment (auto-generate if None)

    Returns:
        Path to the best model checkpoint
    """
    # Load configurations
    dataset_config_path = Path(dataset_config_path)
    model_config_path = Path(model_config_path)

    with open(dataset_config_path, 'r') as f:
        dataset_config = yaml.safe_load(f)

    base_model_config = load_config(model_config_path)

    # Generate experiment name if not provided
    if experiment_name is None:
        session_name = dataset_config.get('session', 'unknown')
        model_name = get_name_from_config(base_model_config)
        timestamp = int(time.time())
        experiment_name = f"ray_tune_{session_name}_{model_name}_{timestamp}"

    print(f"Starting hyperparameter search: {experiment_name}")
    print(f"Dataset: {dataset_config.get('session', 'unknown')} ({len(dataset_config.get('cids', []))} units)")
    print(f"Model: {base_model_config.get('model_type', 'unknown')}")
    print(f"Trials: {num_trials}, Max epochs per trial: {max_epochs_per_trial}")

    # Prepare data once (shared across all trials)
    print("Preparing data...")
    train_dset, val_dset, dataset_config = prepare_data(dataset_config)

    # Auto-detect resources if not specified
    if num_gpus is None:
        num_gpus = torch.cuda.device_count()
    if num_cpus is None:
        num_cpus = os.cpu_count()

    print(f"Ray resources: {num_cpus} CPUs, {num_gpus} GPUs")

    # Initialize Ray
    if not ray.is_initialized():
        ray.init(num_cpus=num_cpus, num_gpus=num_gpus, include_dashboard=False)

    # Define search space
    search_space = get_search_space(base_model_config)

    # Setup Optuna search algorithm
    optuna_sampler = optuna.samplers.TPESampler(
        n_startup_trials=min(20, num_trials // 5),  # 20% random trials
        multivariate=True,
        seed=42
    )

    search_alg = OptunaSearch(
        metric="val_loss",
        mode="min",
        sampler=optuna_sampler
    )

    # Setup ASHA scheduler for early stopping
    # Ensure grace_period <= max_t
    grace_period = min(3, max_epochs_per_trial - 1, max_epochs_per_trial // 2)
    grace_period = max(1, grace_period)  # At least 1 epoch

    scheduler = ASHAScheduler(
        time_attr="epoch",
        metric="val_loss",
        mode="min",
        max_t=max_epochs_per_trial,
        grace_period=grace_period,
        reduction_factor=3
    )

    # Create experiment directory
    experiment_dir = Path(f"/mnt/ssd/YatesMarmoV1/conv_model_fits/ray_experiments/{experiment_name}")
    experiment_dir.mkdir(parents=True, exist_ok=True)

    print(f"Experiment directory: {experiment_dir}")

    # Define the trainable function for Ray
    def trainable(config_dict):
        trial_name = f"trial_{int(time.time() * 1000) % 1000000}"
        train_trial(
            config_dict=config_dict,
            base_model_config=base_model_config,
            dataset_config=dataset_config,
            train_dset=train_dset,
            val_dset=val_dset,
            trial_name=trial_name,
            max_epochs=max_epochs_per_trial
        )

    # Run the hyperparameter search
    print("Starting Ray Tune experiment...")

    # Use the new Ray Tune API
    from ray.train import RunConfig
    from ray.tune import TuneConfig

    run_config = RunConfig(
        name=experiment_name,
        storage_path=str(experiment_dir),
        checkpoint_config=None,  # Disable Ray's checkpointing (we handle our own)
        verbose=1,
        failure_config=None,  # Let individual trials handle failures
    )

    tune_config = TuneConfig(
        search_alg=search_alg,
        scheduler=scheduler,
        num_samples=num_trials,
        max_concurrent_trials=4,  # Limit concurrent trials
    )

    # Create tuner
    tuner = tune.Tuner(
        trainable,
        param_space=search_space,
        tune_config=tune_config,
        run_config=run_config,
    )

    # Run the experiment
    analysis = tuner.fit()

    # Get best trial results
    best_result = analysis.get_best_result("val_loss", "min")
    best_config = best_result.config
    best_val_loss = best_result.metrics["val_loss"]

    print("\n" + "="*50)
    print("HYPERPARAMETER SEARCH COMPLETE")
    print("="*50)
    print(f"Best trial: {best_result.path}")
    print(f"Best validation loss: {best_val_loss:.6f}")
    print("\nBest hyperparameters:")
    for key, value in best_config.items():
        print(f"  {key}: {value}")

    # Train final model with best hyperparameters
    print("\nTraining final model with best hyperparameters...")
    final_model_path = train_final_model(
        best_config=best_config,
        base_model_config=base_model_config,
        dataset_config=dataset_config,
        dataset_config_path=dataset_config_path,
        train_dset=train_dset,
        val_dset=val_dset,
        experiment_name=experiment_name,
        max_epochs=50  # Train longer for final model
    )

    # Save experiment summary
    summary = {
        'experiment_name': experiment_name,
        'dataset_config_path': str(dataset_config_path),
        'model_config_path': str(model_config_path),
        'num_trials': num_trials,
        'best_trial_path': str(best_result.path),
        'best_val_loss': best_val_loss,
        'best_config': best_config,
        'final_model_path': final_model_path,
        'session': dataset_config.get('session', 'unknown'),
        'num_units': len(dataset_config.get('cids', [])),
    }

    summary_path = experiment_dir / "experiment_summary.yaml"
    with open(summary_path, 'w') as f:
        yaml.dump(summary, f, default_flow_style=False)

    print(f"\nExperiment summary saved to: {summary_path}")
    print(f"Final model saved to: {final_model_path}")

    return final_model_path

def train_final_model(best_config: Dict[str, Any],
                     base_model_config: Dict[str, Any],
                     dataset_config: Dict[str, Any],
                     dataset_config_path: Path,
                     train_dset, val_dset,
                     experiment_name: str,
                     max_epochs: int = 50) -> str:
    """
    Train the final model with the best hyperparameters found.

    Args:
        best_config: Best hyperparameters from the search
        base_model_config: Base model configuration
        dataset_config: Dataset configuration
        dataset_config_path: Path to dataset config (for registry)
        train_dset, val_dset: Training and validation datasets
        experiment_name: Name of the experiment
        max_epochs: Maximum epochs for final training

    Returns:
        Path to the best checkpoint of the final model
    """
    # Modify config with best parameters
    model_config = modify_config_with_trial_params(base_model_config, best_config)
    model_config['readout']['params']['n_units'] = len(dataset_config['cids'])

    # Create dataloaders
    train_loader, val_loader = create_dataloaders(
        train_dset, val_dset,
        batch_size=best_config['batch_size'],
        num_workers=4  # More workers for final training
    )

    # Calculate baseline rates
    baseline_rates = calculate_baseline_rates(train_loader)
    inv_softplus = lambda x, beta=1: torch.log(torch.exp(beta*x) - 1) / beta

    # Create Lightning module
    pl_model = PLCoreVisionModel(
        model_class=build_model,
        model_config=model_config,
        optimizer='AdamW',
        optim_kwargs={
            'lr': best_config['lr'],
            'weight_decay': best_config['weight_decay']
        },
        accumulate_grad_batches=1,
        dataset_info=dataset_config
    )

    # Initialize model
    initialize_model_components(pl_model.model, init_bias=inv_softplus(baseline_rates))

    # Create final model name
    session_name = dataset_config.get('session', 'unknown')
    model_name = f"ray_tuned_{session_name}_{experiment_name}_{codename.codename()}"

    # Create checkpoint directory
    checkpoint_dir = Path(f"/mnt/ssd/YatesMarmoV1/conv_model_fits/runs/{model_name}")
    checkpoint_dir.mkdir(parents=True, exist_ok=True)

    # Setup callbacks for final training
    checkpoint_callback = ModelCheckpoint(
        dirpath=str(checkpoint_dir),
        filename=model_name+'-{epoch:02d}-{val_loss:.4f}',
        monitor='val_loss',
        mode='min',
        save_top_k=2,
        save_last=True
    )

    early_stopping_callback = EarlyStopping(
        monitor='val_loss',
        patience=10,  # More patience for final model
        verbose=True,
        mode='min'
    )

    callbacks = [checkpoint_callback, early_stopping_callback, ValidateOnTrainStart()]

    # Create trainer with full logging
    trainer = pl.Trainer(
        callbacks=callbacks,
        accelerator='gpu' if torch.cuda.is_available() else 'cpu',
        devices=1,
        max_epochs=max_epochs,
        logger=WandbLogger(
            project='Digital Twin Ray Tuning',
            name=model_name,
            save_code=True,
            entity='yateslab',
            save_dir=str(checkpoint_dir),
        ),
        check_val_every_n_epoch=1,
    )

    # Train final model
    print(f"Training final model: {model_name}")
    trainer.fit(pl_model, train_loader, val_loader)

    # Get best model path
    best_model_path = checkpoint_callback.best_model_path

    # Register the model
    model_id = f"{model_name}_epoch_{trainer.current_epoch}"

    registry.register_model(
        model_id=model_id,
        checkpoint_path=best_model_path,
        config_path=str(dataset_config_path),  # This should be model config path
        metrics={'val_loss': trainer.callback_metrics.get('val_loss', 0).item()},
        metadata={
            'experiment_name': experiment_name,
            'epochs': trainer.current_epoch,
            'best_hyperparameters': best_config,
            'session': dataset_config.get('session', 'unknown'),
            'num_units': len(dataset_config.get('cids', [])),
            'ray_tuned': True
        },
        dataset_config_path=str(dataset_config_path)
    )

    print(f"Final model registered as: {model_id}")
    return best_model_path

def run_batch_experiments(dataset_configs_dir: str,
                         model_config_path: str,
                         num_trials: int = 50,
                         max_epochs_per_trial: int = 25,
                         pattern: str = "*.yaml") -> List[str]:
    """
    Run hyperparameter search on multiple datasets in batch.

    Args:
        dataset_configs_dir: Directory containing dataset configurations
        model_config_path: Path to model configuration
        num_trials: Number of trials per dataset
        max_epochs_per_trial: Maximum epochs per trial
        pattern: File pattern to match dataset configs

    Returns:
        List of paths to best models for each dataset
    """
    dataset_configs_dir = Path(dataset_configs_dir)
    dataset_config_paths = list(dataset_configs_dir.glob(pattern))

    print(f"Found {len(dataset_config_paths)} dataset configurations")

    best_models = []
    failed_datasets = []

    for i, dataset_config_path in enumerate(dataset_config_paths):
        print(f"\n{'='*60}")
        print(f"Processing dataset {i+1}/{len(dataset_config_paths)}: {dataset_config_path.name}")
        print(f"{'='*60}")

        try:
            best_model_path = run_hyperparameter_search(
                dataset_config_path=str(dataset_config_path),
                model_config_path=model_config_path,
                num_trials=num_trials,
                max_epochs_per_trial=max_epochs_per_trial,
                experiment_name=f"batch_{dataset_config_path.stem}"
            )
            best_models.append(best_model_path)
            print(f"✅ Successfully completed: {dataset_config_path.name}")

        except Exception as e:
            print(f"❌ Failed to process {dataset_config_path.name}: {e}")
            failed_datasets.append(str(dataset_config_path))
            best_models.append(None)

    # Print summary
    print(f"\n{'='*60}")
    print("BATCH EXPERIMENT SUMMARY")
    print(f"{'='*60}")
    print(f"Total datasets: {len(dataset_config_paths)}")
    print(f"Successful: {len([m for m in best_models if m is not None])}")
    print(f"Failed: {len(failed_datasets)}")

    if failed_datasets:
        print("\nFailed datasets:")
        for dataset in failed_datasets:
            print(f"  - {dataset}")

    return best_models

def parse_arguments():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description='Hyperparameter tuning with Ray and Optuna')

    # Required arguments
    parser.add_argument('--dataset-config', type=str, required=True,
                        help='Path to dataset configuration file')
    parser.add_argument('--model-config', type=str, required=True,
                        help='Path to model configuration file')

    # Hyperparameter search options
    parser.add_argument('--num-trials', type=int, default=100,
                        help='Number of trials to run (default: 100)')
    parser.add_argument('--max-epochs-per-trial', type=int, default=25,
                        help='Maximum epochs per trial (default: 25)')

    # Resource allocation
    parser.add_argument('--num-gpus', type=int, default=None,
                        help='Number of GPUs to use (default: auto-detect)')
    parser.add_argument('--num-cpus', type=int, default=None,
                        help='Number of CPUs to use (default: auto-detect)')

    # Experiment management
    parser.add_argument('--experiment-name', type=str, default=None,
                        help='Name for the experiment (default: auto-generate)')
    parser.add_argument('--resume', action='store_true',
                        help='Resume from previous experiment')

    # Batch processing
    parser.add_argument('--batch-mode', action='store_true',
                        help='Run on all datasets in a directory')
    parser.add_argument('--dataset-configs-dir', type=str,
                        help='Directory containing dataset configs (for batch mode)')
    parser.add_argument('--pattern', type=str, default="*.yaml",
                        help='File pattern for dataset configs (default: *.yaml)')

    return parser.parse_args()

if __name__ == "__main__":
    args = parse_arguments()

    print("🚀 Ray Hyperparameter Tuning for Neural Network Models")
    print("="*60)

    if args.batch_mode:
        if not args.dataset_configs_dir:
            raise ValueError("--dataset-configs-dir is required for batch mode")

        print(f"Running in BATCH MODE")
        print(f"Dataset configs directory: {args.dataset_configs_dir}")
        print(f"Model config: {args.model_config}")
        print(f"Trials per dataset: {args.num_trials}")
        print(f"Max epochs per trial: {args.max_epochs_per_trial}")

        best_models = run_batch_experiments(
            dataset_configs_dir=args.dataset_configs_dir,
            model_config_path=args.model_config,
            num_trials=args.num_trials,
            max_epochs_per_trial=args.max_epochs_per_trial,
            pattern=args.pattern
        )

        print(f"\n🎉 Batch processing complete!")
        print(f"Processed {len(best_models)} datasets")

    else:
        print(f"Running in SINGLE DATASET MODE")
        print(f"Dataset config: {args.dataset_config}")
        print(f"Model config: {args.model_config}")
        print(f"Number of trials: {args.num_trials}")
        print(f"Max epochs per trial: {args.max_epochs_per_trial}")

        best_model_path = run_hyperparameter_search(
            dataset_config_path=args.dataset_config,
            model_config_path=args.model_config,
            num_trials=args.num_trials,
            max_epochs_per_trial=args.max_epochs_per_trial,
            num_gpus=args.num_gpus,
            num_cpus=args.num_cpus,
            resume=args.resume,
            experiment_name=args.experiment_name
        )

        print(f"\n🎉 Hyperparameter search complete!")
        print(f"Best model: {best_model_path}")

    print("\nDone! 🎯")
